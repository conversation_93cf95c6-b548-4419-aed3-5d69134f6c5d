<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Authentication routes
$routes->post('login', 'Auth::login');
$routes->get('logout', 'Auth::logout');

// Dashboard routes
$routes->get('dashboard', 'Dashboard::index', ['filter' => 'auth']);

// Admin routes
$routes->group('admin', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'AdminController::dashboard');
    $routes->get('dashboard', 'AdminController::dashboard');
    
    // Products
    $routes->get('products', 'AdminController::products');
    $routes->get('products/create', 'AdminController::createProduct');
    $routes->get('products/edit/(:num)', 'AdminController::editProduct/$1');
    $routes->get('products/view/(:num)', 'AdminController::viewProduct/$1');
    $routes->post('products/store', 'AdminController::storeProduct');
    $routes->post('products/update/(:num)', 'AdminController::updateProduct/$1');
    $routes->delete('products/delete/(:num)', 'AdminController::deleteProduct/$1');
    
    // Users Management (RESTful routes)
    $routes->get('users', 'UsersController::index');                    // GET /admin/users - List all users
    $routes->get('users/create', 'UsersController::create');            // GET /admin/users/create - Show create form
    $routes->post('users', 'UsersController::store');                   // POST /admin/users - Store new user
    $routes->get('users/(:num)', 'UsersController::show/$1');           // GET /admin/users/{id} - Show user details
    $routes->get('users/(:num)/edit', 'UsersController::edit/$1');      // GET /admin/users/{id}/edit - Show edit form
    $routes->put('users/(:num)', 'UsersController::update/$1');         // PUT /admin/users/{id} - Update user
    $routes->patch('users/(:num)', 'UsersController::update/$1');       // PATCH /admin/users/{id} - Update user
    $routes->delete('users/(:num)', 'UsersController::delete/$1');      // DELETE /admin/users/{id} - Delete user
    $routes->post('users/bulk-action', 'UsersController::bulkAction');  // POST /admin/users/bulk-action - Bulk actions
    $routes->post('users/export', 'UsersController::export');           // POST /admin/users/export - Export users
    
    // Orders
    $routes->get('orders', 'AdminController::orders');
    $routes->get('orders/view/(:num)', 'AdminController::viewOrder/$1');
    $routes->post('orders/update-status/(:num)', 'AdminController::updateOrderStatus/$1');
    
    // Analytics
    $routes->get('analytics', 'AdminController::analytics');
    
    // Settings
    $routes->get('settings', 'AdminController::settings');
    $routes->post('settings/update', 'AdminController::updateSettings');
    
    // Categories
    $routes->get('categories', 'AdminController::categories');
    $routes->post('categories/store', 'AdminController::storeCategory');
    $routes->post('categories/update/(:num)', 'AdminController::updateCategory/$1');
    $routes->delete('categories/delete/(:num)', 'AdminController::deleteCategory/$1');
    
    // Farmers and Buyers
    $routes->get('farmers', 'AdminController::farmers');
    $routes->get('buyers', 'AdminController::buyers');
    
    // Inventory
    $routes->get('inventory', 'AdminController::inventory');
    
    // Transactions
    $routes->get('transactions', 'AdminController::transactions');
    
    // System
    $routes->get('system', 'AdminController::system');
    
    // Notifications
    $routes->get('notifications', 'AdminController::notifications');
    $routes->post('notifications/mark-read', 'AdminController::markNotificationRead');
    
    // Profile
    $routes->get('profile', 'AdminController::profile');
    $routes->post('profile/update', 'AdminController::updateProfile');
});

// Setup routes (development only)
$routes->get('setup', 'Setup::index');
$routes->get('setup/testConnection', 'Setup::testConnection');
$routes->get('setup/testRawConnection', 'Setup::testRawConnection');
$routes->get('setup/createInitialData', 'Setup::createInitialData');
$routes->get('setup/testLogin', 'Setup::testLogin');

