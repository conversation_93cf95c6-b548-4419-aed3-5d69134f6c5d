<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .form-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .form-select:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .role-checkboxes {
        display: flex;
        gap: 1.5rem;
        flex-wrap: wrap;
    }
    
    .role-checkbox {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }
    
    .role-checkbox:hover {
        background: #e9ecef;
    }
    
    .role-checkbox.checked {
        background: rgba(46, 125, 50, 0.1);
        border-color: #2E7D32;
    }
    
    .role-checkbox input[type="checkbox"] {
        margin: 0;
    }
    
    .role-checkbox label {
        margin: 0;
        font-weight: 500;
        cursor: pointer;
    }
    
    .form-actions {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }
    
    .btn-admin-primary {
        background: #2E7D32;
        border-color: #2E7D32;
        color: white;
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-admin-primary:hover {
        background: #1B5E20;
        border-color: #1B5E20;
        color: white;
    }
    
    .btn-secondary {
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 8px;
    }
    
    .alert {
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }
    
    .required {
        color: #dc3545;
    }
    
    .user-info-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #2E7D32;
    }
    
    .user-info-card h5 {
        color: #2E7D32;
        margin-bottom: 0.5rem;
    }
    
    .user-info-card .text-muted {
        font-size: 0.875rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= $page_title ?></h1>
            <?php if (isset($page_description)): ?>
                <p class="mb-0 text-muted"><?= $page_description ?></p>
            <?php endif; ?>
        </div>
        <div>
            <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="user-info-card">
        <h5><i class="fas fa-user me-2"></i>Editing User: <?= esc($user['fullname']) ?></h5>
        <div class="text-muted">
            <strong>Username:</strong> <?= esc($user['username']) ?> | 
            <strong>Email:</strong> <?= esc($user['email']) ?> | 
            <strong>Created:</strong> <?= date('M j, Y', strtotime($user['created_at'])) ?>
        </div>
    </div>

    <!-- Display validation errors -->
    <?php if (session('errors')): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach (session('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (session('error')): ?>
        <div class="alert alert-danger">
            <?= esc(session('error')) ?>
        </div>
    <?php endif; ?>

    <?php if (session('success')): ?>
        <div class="alert alert-success">
            <?= esc(session('success')) ?>
        </div>
    <?php endif; ?>

    <!-- Edit User Form -->
    <form action="<?= base_url('admin/users/' . $user['id']) ?>" method="post" id="editUserForm">
        <?= csrf_field() ?>
        <input type="hidden" name="_method" value="PUT">
        
        <!-- Basic Information Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-user me-2"></i>Basic Information
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="fullname" class="form-label">
                            Full Name <span class="required">*</span>
                        </label>
                        <input type="text" class="form-control" id="fullname" name="fullname" 
                               value="<?= old('fullname', $user['fullname']) ?>" required>
                        <div class="form-text">Enter the user's complete full name</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            Username <span class="required">*</span>
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?= old('username', $user['username']) ?>" 
                               pattern="[a-zA-Z0-9_]{3,50}" 
                               title="Username must be 3-50 characters long and contain only letters, numbers, and underscores"
                               required>
                        <div class="form-text">Unique username for login (3-50 characters)</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email" class="form-label">
                            Email Address <span class="required">*</span>
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= old('email', $user['email']) ?>" required>
                        <div class="form-text">Valid email address for login and notifications</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="status" class="form-label">
                            Account Status <span class="required">*</span>
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status', $user['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status', $user['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            <option value="suspended" <?= old('status', $user['status']) === 'suspended' ? 'selected' : '' ?>>Suspended</option>
                            <option value="pending" <?= old('status', $user['status']) === 'pending' ? 'selected' : '' ?>>Pending</option>
                        </select>
                        <div class="form-text">Current status of the user account</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-lock me-2"></i>Security & Authentication
            </h3>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Password Update:</strong> Leave password fields blank to keep the current password unchanged.
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="password" name="password" 
                               minlength="8">
                        <div class="form-text">Leave blank to keep current password</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                        <div class="form-text">Re-enter the new password to confirm</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Roles Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-shield-alt me-2"></i>Roles & Permissions
            </h3>
            
            <div class="form-group">
                <label class="form-label">User Roles</label>
                <div class="role-checkboxes">
                    <div class="role-checkbox">
                        <input type="checkbox" id="is_admin" name="is_admin" value="1"
                               <?= old('is_admin', $user['is_admin']) ? 'checked' : '' ?>>
                        <label for="is_admin">
                            <strong>Administrator</strong><br>
                            <small>Full system access and management</small>
                        </label>
                    </div>
                    
                    <div class="role-checkbox">
                        <input type="checkbox" id="is_supervisor" name="is_supervisor" value="1"
                               <?= old('is_supervisor', $user['is_supervisor']) ? 'checked' : '' ?>>
                        <label for="is_supervisor">
                            <strong>Supervisor</strong><br>
                            <small>Manage users and monitor operations</small>
                        </label>
                    </div>
                    
                    <div class="role-checkbox">
                        <input type="checkbox" id="is_buyer" name="is_buyer" value="1"
                               <?= old('is_buyer', $user['is_buyer']) ? 'checked' : '' ?>>
                        <label for="is_buyer">
                            <strong>Buyer</strong><br>
                            <small>Purchase commodities and manage orders</small>
                        </label>
                    </div>
                </div>
                <div class="form-text">Select one or more roles for this user. Users can have multiple roles.</div>
            </div>
        </div>

        <!-- Additional Information Section -->
        <div class="form-section">
            <h3 class="form-section-title">
                <i class="fas fa-info-circle me-2"></i>Additional Information
            </h3>
            
            <div class="form-group">
                <label for="remarks" class="form-label">Remarks</label>
                <textarea class="form-control" id="remarks" name="remarks" rows="4" 
                          placeholder="Optional notes or comments about this user..."><?= old('remarks', $user['remarks']) ?></textarea>
                <div class="form-text">Optional internal notes about this user account</div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                <i class="fas fa-times me-2"></i>Cancel
            </a>
            <button type="submit" class="btn btn-admin-primary no-loading-state" id="updateUserBtn">
                <i class="fas fa-save me-2"></i>Update User
            </button>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle role checkbox styling
    const roleCheckboxes = document.querySelectorAll('.role-checkbox input[type="checkbox"]');

    roleCheckboxes.forEach(checkbox => {
        const container = checkbox.closest('.role-checkbox');
        updateCheckboxStyling(checkbox, container);

        checkbox.addEventListener('change', function() {
            updateCheckboxStyling(this, container);
        });
    });

    function updateCheckboxStyling(checkbox, container) {
        if (checkbox.checked) {
            container.classList.add('checked');
        } else {
            container.classList.remove('checked');
        }
    }

    // Password confirmation validation
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');

    function validatePasswordMatch() {
        if (passwordField.value && confirmPasswordField.value) {
            if (passwordField.value !== confirmPasswordField.value) {
                confirmPasswordField.setCustomValidity('Passwords do not match');
                confirmPasswordField.classList.add('is-invalid');
            } else {
                confirmPasswordField.setCustomValidity('');
                confirmPasswordField.classList.remove('is-invalid');
                confirmPasswordField.classList.add('is-valid');
            }
        } else {
            confirmPasswordField.setCustomValidity('');
            confirmPasswordField.classList.remove('is-invalid', 'is-valid');
        }
    }

    if (passwordField && confirmPasswordField) {
        passwordField.addEventListener('input', validatePasswordMatch);
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }

    // Username validation (alphanumeric and underscores only)
    const usernameField = document.getElementById('username');
    if (usernameField) {
        usernameField.addEventListener('input', function() {
            this.value = this.value.replace(/[^a-zA-Z0-9_]/g, '');
        });
    }

    // Prevent global loading state handler from interfering with this form
    const updateUserBtn = document.getElementById('updateUserBtn');
    if (updateUserBtn) {
        // Remove any existing event listeners that might interfere
        updateUserBtn.addEventListener('click', function(e) {
            // Stop the global loading state handler from running
            e.stopImmediatePropagation();
        }, true); // Use capture phase to run before other handlers
    }

    // Form submission validation - Standard CodeIgniter 4 form submission
    const editUserForm = document.getElementById('editUserForm');
    if (editUserForm) {
        editUserForm.addEventListener('submit', function(e) {
            // Check if at least one role is selected
            const roleCheckboxes = document.querySelectorAll('input[name="is_admin"], input[name="is_supervisor"], input[name="is_buyer"]');
            const hasRole = Array.from(roleCheckboxes).some(cb => cb.checked);

            if (!hasRole) {
                if (!confirm('No roles selected. Are you sure you want to continue? The user will have no permissions.')) {
                    e.preventDefault();
                    return false;
                }
            }

            // Validate password match if passwords are provided
            if (passwordField && confirmPasswordField && passwordField.value && confirmPasswordField.value) {
                if (passwordField.value !== confirmPasswordField.value) {
                    alert('Passwords do not match. Please check and try again.');
                    confirmPasswordField.focus();
                    e.preventDefault();
                    return false;
                }
            }

            // Allow normal form submission to proceed
            // The form will submit to the server using standard CodeIgniter 4 form submission
            return true;
        });
    }
});
</script>
<?= $this->endSection() ?>
