// Admin Template JavaScript for DCBuyer

document.addEventListener('DOMContentLoaded', function() {
    initializeAdminTemplate();
});

function initializeAdminTemplate() {
    // Initialize sidebar functionality
    initializeSidebar();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize auto-dismiss alerts
    initializeAlerts();
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize loading states
    initializeLoadingStates();
    
    // Initialize responsive features
    initializeResponsive();
}

function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const sidebarCollapse = document.getElementById('sidebarCollapse');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileToggle = document.getElementById('mobileToggle');
    
    // Desktop sidebar collapse
    if (sidebarCollapse) {
        sidebarCollapse.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('sidebar-collapsed');
            
            // Save state to localStorage
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebarCollapsed', isCollapsed);
        });
    }
    
    // Mobile sidebar toggle
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            document.body.classList.toggle('sidebar-open');
        });
    }
    
    // Sidebar close button (mobile)
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.remove('show');
            document.body.classList.remove('sidebar-open');
        });
    }
    
    // Close sidebar when clicking outside (mobile)
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 991) {
            const isClickInsideSidebar = sidebar.contains(e.target);
            const isClickOnToggle = mobileToggle && mobileToggle.contains(e.target);
            
            if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }
        }
    });
    
    // Restore sidebar state from localStorage
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true' && window.innerWidth > 991) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('sidebar-collapsed');
    }
    
    // Handle dropdown menus in sidebar
    const dropdownToggles = sidebar.querySelectorAll('[data-bs-toggle="collapse"]');
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('data-bs-target'));
            if (target) {
                // Close other open dropdowns
                dropdownToggles.forEach(otherToggle => {
                    if (otherToggle !== this) {
                        const otherTarget = document.querySelector(otherToggle.getAttribute('data-bs-target'));
                        if (otherTarget && otherTarget.classList.contains('show')) {
                            otherTarget.classList.remove('show');
                            otherToggle.classList.remove('active');
                        }
                    }
                });
                
                // Toggle current dropdown
                target.classList.toggle('show');
                this.classList.toggle('active');
            }
        });
    });
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-tooltip]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-popover]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

function initializeAlerts() {
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    });
}

function initializeSearch() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300); // Debounce search
            }
        });
        
        // Handle search form submission
        const searchForm = searchInput.closest('form') || searchInput.parentElement;
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const query = searchInput.value.trim();
                if (query.length >= 2) {
                    performSearch(query);
                }
            });
        }
    }
}

function performSearch(query) {
    // This is a placeholder for search functionality
    // You can implement AJAX search here
    console.log('Searching for:', query);
    
    // Example: Show search results in a dropdown
    showSearchResults([
        { title: 'Sample Result 1', url: '#' },
        { title: 'Sample Result 2', url: '#' }
    ]);
}

function showSearchResults(results) {
    // Create or update search results dropdown
    let resultsContainer = document.querySelector('.search-results');
    if (!resultsContainer) {
        resultsContainer = document.createElement('div');
        resultsContainer.className = 'dropdown-menu show search-results';
        resultsContainer.style.position = 'absolute';
        resultsContainer.style.top = '100%';
        resultsContainer.style.left = '0';
        resultsContainer.style.right = '0';
        resultsContainer.style.zIndex = '1050';
        
        const searchWrapper = document.querySelector('.search-wrapper');
        if (searchWrapper) {
            searchWrapper.style.position = 'relative';
            searchWrapper.appendChild(resultsContainer);
        }
    }
    
    if (results.length > 0) {
        resultsContainer.innerHTML = results.map(result => 
            `<a class="dropdown-item" href="${result.url}">${result.title}</a>`
        ).join('');
    } else {
        resultsContainer.innerHTML = '<div class="dropdown-item-text">No results found</div>';
    }
    
    // Hide results when clicking outside
    document.addEventListener('click', function hideResults(e) {
        if (!resultsContainer.contains(e.target) && !document.querySelector('.search-input').contains(e.target)) {
            resultsContainer.remove();
            document.removeEventListener('click', hideResults);
        }
    });
}

function initializeLoadingStates() {
    // Add loading states to buttons and forms (exclude buttons with no-loading-state class)
    const buttons = document.querySelectorAll('.btn[type="submit"]:not(.no-loading-state), .btn-loading');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled && !this.classList.contains('loading')) {
                addLoadingState(this);

                // For forms, remove loading state when form is submitted
                const form = this.closest('form');
                if (form) {
                    form.addEventListener('submit', () => {
                        setTimeout(() => removeLoadingState(this), 100);
                    });
                }
            }
        });
    });
    
    // Handle AJAX form submissions
    const ajaxForms = document.querySelectorAll('.ajax-form');
    ajaxForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const submitBtn = this.querySelector('[type="submit"]');
            if (submitBtn) addLoadingState(submitBtn);
            
            // Simulate AJAX request (replace with actual AJAX call)
            setTimeout(() => {
                if (submitBtn) removeLoadingState(submitBtn);
                showNotification('Form submitted successfully!', 'success');
            }, 2000);
        });
    });
}

function addLoadingState(element) {
    element.classList.add('loading');
    element.disabled = true;
    
    const originalText = element.innerHTML;
    element.setAttribute('data-original-text', originalText);
    element.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
}

function removeLoadingState(element) {
    element.classList.remove('loading');
    element.disabled = false;
    
    const originalText = element.getAttribute('data-original-text');
    if (originalText) {
        element.innerHTML = originalText;
        element.removeAttribute('data-original-text');
    }
}

function initializeResponsive() {
    // Handle window resize
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        if (window.innerWidth <= 991) {
            // Mobile view
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('sidebar-collapsed');
            sidebar.classList.remove('show');
            document.body.classList.remove('sidebar-open');
        } else {
            // Desktop view
            const savedState = localStorage.getItem('sidebarCollapsed');
            if (savedState === 'true') {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('sidebar-collapsed');
            }
        }
    });
}

// Utility functions
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px; 
        right: 20px; 
        z-index: 9999; 
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    let icon = 'fas fa-info-circle';
    switch(type) {
        case 'success': icon = 'fas fa-check-circle'; break;
        case 'error': 
        case 'danger': icon = 'fas fa-exclamation-triangle'; break;
        case 'warning': icon = 'fas fa-exclamation-circle'; break;
    }
    
    notification.innerHTML = `
        <i class="${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove
    setTimeout(() => {
        if (notification.parentNode) {
            const bsAlert = new bootstrap.Alert(notification);
            bsAlert.close();
        }
    }, duration);
}

function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// Data table helpers
function initializeDataTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (table) {
        // Add sorting functionality
        const headers = table.querySelectorAll('th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(table, this);
            });
        });
        
        // Add search functionality if search input exists
        const searchInput = document.querySelector(`[data-table="${tableId}"]`);
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                filterTable(table, this.value);
            });
        }
    }
}

function sortTable(table, header) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentElement.children).indexOf(header);
    const isAscending = !header.classList.contains('sort-asc');
    
    // Remove existing sort classes
    header.parentElement.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // Add new sort class
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    
    // Sort rows
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        const comparison = aValue.localeCompare(bValue, undefined, { numeric: true });
        return isAscending ? comparison : -comparison;
    });
    
    // Reorder rows in table
    rows.forEach(row => tbody.appendChild(row));
}

function filterTable(table, searchTerm) {
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const match = text.includes(searchTerm.toLowerCase());
        row.style.display = match ? '' : 'none';
    });
}

// Export functions for global use
window.AdminTemplate = {
    showNotification,
    confirmAction,
    addLoadingState,
    removeLoadingState,
    initializeDataTable,
    sortTable,
    filterTable
};